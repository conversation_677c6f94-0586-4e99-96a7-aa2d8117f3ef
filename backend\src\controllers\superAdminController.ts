import { Request, Response } from 'express';
import { UserRole } from '../models/supabase';
import { Tenant, User, Appointment, Service } from '../utils/modelHelpers';

// Get all tenants (businesses)
export const getAllTenants = async (req: Request, res: Response) => {
  try {
    console.log('Fetching all tenants...');

    const tenants = await Tenant.findAll({
      attributes: ['id', 'name', 'email', 'phone', 'address', 'active', 'createdAt'],
      order: [['createdAt', 'DESC']]
    });

    console.log(`Successfully fetched ${tenants.length} tenants`);

    return res.status(200).json({
      success: true,
      data: tenants
    });
  } catch (error) {
    console.error('Error fetching tenants:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch tenants',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};

// Get detailed tenant information including stats
export const getTenantDetails = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    const tenant = await Tenant.findByPk(tenantId);
    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found'
      });
    }

    // Get tenant stats
    const userCount = await User.count({ where: { tenantId } });
    const appointmentCount = await Appointment.count({ where: { tenantId } });
    const serviceCount = await Service.count({ where: { tenantId } });

    return res.status(200).json({
      success: true,
      data: {
        tenant,
        stats: {
          users: userCount,
          appointments: appointmentCount,
          services: serviceCount
        }
      }
    });
  } catch (error) {
    console.error('Error fetching tenant details:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch tenant details'
    });
  }
};

// Update tenant status (activate/deactivate)
export const updateTenantStatus = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;
    const { active } = req.body;

    const tenant = await Tenant.findByPk(tenantId);
    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found'
      });
    }

    await Tenant.update(
      { active },
      {
        where: {
          id: tenantId
        }
      }
    );

    // Get the updated tenant data
    const refreshedTenant = await Tenant.findByPk(tenantId);

    return res.status(200).json({
      success: true,
      message: `Tenant ${active ? 'activated' : 'deactivated'} successfully`,
      data: refreshedTenant
    });
  } catch (error) {
    console.error('Error updating tenant status:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to update tenant status'
    });
  }
};

// Delete a tenant
export const deleteTenant = async (req: Request, res: Response) => {
  try {
    const { tenantId } = req.params;

    const tenant = await Tenant.findByPk(tenantId);
    if (!tenant) {
      return res.status(404).json({
        success: false,
        message: 'Tenant not found'
      });
    }

    await Tenant.destroy({
      where: {
        id: tenantId
      }
    });

    return res.status(200).json({
      success: true,
      message: 'Tenant deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting tenant:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to delete tenant'
    });
  }
};

// Get super admin dashboard data
export const getSuperAdminDashboard = async (req: Request, res: Response) => {
  try {
    console.log('Fetching super admin dashboard data...');

    // Get counts with error handling for each operation
    let totalTenants = 0;
    let activeTenants = 0;
    let totalUsers = 0;
    let totalAppointments = 0;
    let recentTenants = [];

    try {
      totalTenants = await Tenant.count();
      console.log('Total tenants count:', totalTenants);
    } catch (err) {
      console.error('Error counting tenants:', err);
    }

    try {
      activeTenants = await Tenant.count({ where: { active: true } });
      console.log('Active tenants count:', activeTenants);
    } catch (err) {
      console.error('Error counting active tenants:', err);
    }

    try {
      totalUsers = await User.count();
      console.log('Total users count:', totalUsers);
    } catch (err) {
      console.error('Error counting users:', err);
    }

    try {
      totalAppointments = await Appointment.count();
      console.log('Total appointments count:', totalAppointments);
    } catch (err) {
      console.error('Error counting appointments:', err);
    }

    // Get recent tenants
    try {
      recentTenants = await Tenant.findAll({
        order: [['createdAt', 'DESC']],
        limit: 5,
        attributes: ['id', 'name', 'email', 'createdAt', 'active']
      });
      console.log('Recent tenants fetched:', recentTenants.length);
    } catch (err) {
      console.error('Error fetching recent tenants:', err);
    }

    return res.status(200).json({
      success: true,
      data: {
        stats: {
          totalTenants,
          activeTenants,
          totalUsers,
          totalAppointments
        },
        recentTenants
      }
    });
  } catch (error) {
    console.error('Error fetching dashboard data:', error);
    return res.status(500).json({
      success: false,
      message: 'Failed to fetch dashboard data',
      error: error instanceof Error ? error.message : String(error)
    });
  }
};