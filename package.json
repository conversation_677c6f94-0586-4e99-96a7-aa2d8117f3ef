{"name": "scheduly-backend", "version": "1.0.0", "description": "Backend for Scheduly appointment scheduling application", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "test": "jest", "lint": "eslint . --ext .ts"}, "dependencies": {"@supabase/supabase-js": "^2.49.4", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.18.2", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "razorpay": "^2.9.2", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.5", "@types/morgan": "^1.9.9", "@types/node": "^20.11.5", "@types/uuid": "^9.0.7", "nodemon": "^3.0.3", "ts-node": "^10.9.2", "typescript": "^5.3.3"}}