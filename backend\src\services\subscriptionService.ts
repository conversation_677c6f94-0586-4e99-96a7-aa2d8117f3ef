import { createClient } from '@supabase/supabase-js';
import { Subscription, SubscriptionStatus } from '../models/Subscription';
import { TierLevel, SubscriptionTier, getTierById } from '../models/SubscriptionTier';
import { SubscriptionFeature } from '../models/SubscriptionFeature';
import config from '../config';

import {
  sendSubscriptionConfirmationEmail,
  sendSubscriptionUpdatedEmail,
  sendSubscriptionCancellationEmail,
  sendPaymentFailedEmail
} from './emailService';

// Initialize Supabase client
const supabase = createClient(config.supabase.url, config.supabase.serviceKey);

// Get user's active subscription
export async function getUserSubscription(userId: string): Promise<Subscription | null> {
  try {
    const { data, error } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    if (error) {
      console.error('Error fetching subscription:', error);
      return null;
    }

    return data as Subscription;
  } catch (error) {
    console.error('Error in getUserSubscription:', error);
    return null;
  }
}

// Get tier details
export async function getTierDetails(tierId: TierLevel): Promise<SubscriptionTier> {
  const tier = getTierById(tierId);

  if (!tier) {
    throw new Error(`Tier not found: ${tierId}`);
  }

  return tier;
}

// Check if user has access to a feature
export async function userHasFeature(userId: string, feature: SubscriptionFeature): Promise<boolean> {
  try {
    const subscription = await getUserSubscription(userId);

    if (!subscription || subscription.status !== SubscriptionStatus.ACTIVE) {
      return false;
    }

    const tier = getTierById(subscription.tier_id);

    if (!tier) {
      return false;
    }

    return tier.features.includes(feature as any);
  } catch (error) {
    console.error('Error in userHasFeature:', error);
    return false;
  }
}

// Create a new subscription
export async function createSubscription(
  userId: string,
  tierId: TierLevel,
  paymentMethodId?: string,
  razorpayData?: {
    razorpayOrderId?: string;
    razorpayPaymentId?: string;
    razorpayCustomerId?: string;
  }
): Promise<Subscription | null> {
  try {
    const tier = getTierById(tierId);

    if (!tier) {
      throw new Error(`Tier not found: ${tierId}`);
    }

    // For free tier, we don't need to create a Stripe subscription
    if (tierId === TierLevel.FREE) {
      const now = new Date();
      const trialEnd = new Date();
      trialEnd.setDate(now.getDate() + tier.trialDays);

      const subscriptionData = {
        user_id: userId,
        tier_id: tierId,
        status: SubscriptionStatus.ACTIVE,
        current_period_start: now,
        current_period_end: trialEnd,
        cancel_at_period_end: false,
        created_at: now,
        updated_at: now
      };

      const { data, error } = await supabase
        .from('subscriptions')
        .insert(subscriptionData)
        .select()
        .single();

      if (error) {
        console.error('Error creating free subscription:', error);
        return null;
      }

      return data as Subscription;
    }

    // For paid tiers, create a Razorpay subscription

    // Get user details for Razorpay customer creation
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email, first_name, last_name, phone')
      .eq('id', userId)
      .single();

    if (userError) {
      console.error('Error fetching user:', userError);
      return null;
    }

    // Get or create Razorpay customer
    const customerName = `${user.first_name} ${user.last_name}`.trim();
    const customerId = await getOrCreateRazorpayCustomer(userId, user.email, customerName, user.phone);

    if (!customerId) {
      console.error('Failed to create Razorpay customer');
      return null;
    }

    // Create Razorpay subscription plan
    const razorpaySubscription = await createSubscriptionPlan(
      tierId,
      customerId
    );

    if (!razorpaySubscription) {
      console.error('Failed to create Razorpay subscription');
      return null;
    }

    const now = new Date();
    const trialEnd = new Date();
    trialEnd.setDate(now.getDate() + tier.trialDays);

    // Create subscription in our database
    const subscriptionData = {
      user_id: userId,
      tier_id: tierId,
      status: tier.trialDays > 0 ? SubscriptionStatus.TRIALING : SubscriptionStatus.ACTIVE,
      current_period_start: now,
      current_period_end: trialEnd,
      cancel_at_period_end: false,
      payment_method_id: paymentMethodId,
      razorpay_subscription_id: razorpaySubscription.subscriptionId,
      razorpay_customer_id: razorpayData?.razorpayCustomerId || customerId,
      razorpay_plan_id: razorpaySubscription.planId,
      razorpay_order_id: razorpayData?.razorpayOrderId,
      razorpay_payment_id: razorpayData?.razorpayPaymentId,
      created_at: now,
      updated_at: now
    };

    const { data, error } = await supabase
      .from('subscriptions')
      .insert(subscriptionData)
      .select()
      .single();

    if (error) {
      console.error('Error creating subscription in database:', error);
      return null;
    }

    // Send confirmation email
    const { data: userData } = await supabase
      .from('users')
      .select('email, first_name, last_name')
      .eq('id', userId)
      .single();

    if (userData) {
      const userName = `${userData.first_name} ${userData.last_name}`.trim();
      await sendSubscriptionConfirmationEmail(userData.email, userName, tierId);
    }

    return data as Subscription;
  } catch (error) {
    console.error('Error in createSubscription:', error);
    return null;
  }
}

// Update subscription tier
export async function updateSubscriptionTier(
  subscriptionId: string,
  newTierId: TierLevel
): Promise<Subscription | null> {
  try {
    // Get the current subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('id', subscriptionId)
      .single();

    if (subscriptionError) {
      console.error('Error fetching subscription:', subscriptionError);
      return null;
    }

    // If changing to/from free tier, handle differently
    if (newTierId === TierLevel.FREE && subscription.tier_id !== TierLevel.FREE) {
      // If changing from paid to free, cancel the Razorpay subscription
      if (subscription.razorpay_subscription_id) {
        const canceled = await cancelRazorpaySubscription(subscription.razorpay_subscription_id, true);

        if (!canceled) {
          console.error('Failed to cancel Razorpay subscription');
          return null;
        }
      }

      // Update to free tier
      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          tier_id: newTierId,
          status: SubscriptionStatus.ACTIVE,
          updated_at: new Date()
        })
        .eq('id', subscriptionId)
        .select()
        .single();

      if (error) {
        console.error('Error updating to free tier:', error);
        return null;
      }

      return data as Subscription;
    } else if (subscription.tier_id === TierLevel.FREE && newTierId !== TierLevel.FREE) {
      // If changing from free to paid, create a new Stripe subscription

      // Get user details
      const { data: user, error: userError } = await supabase
        .from('users')
        .select('id, email, first_name, last_name')
        .eq('id', subscription.user_id)
        .single();

      if (userError) {
        console.error('Error fetching user:', userError);
        return null;
      }

      // Create a new subscription
      const newSubscription = await createSubscription(user.id, newTierId);

      if (!newSubscription) {
        console.error('Failed to create new subscription');
        return null;
      }

      // Delete the old free subscription
      const { error: deleteError } = await supabase
        .from('subscriptions')
        .delete()
        .eq('id', subscriptionId);

      if (deleteError) {
        console.error('Error deleting old subscription:', deleteError);
      }

      return newSubscription;
    } else {
      // Both tiers are paid, update the Razorpay subscription
      if (subscription.razorpay_subscription_id) {
        const updated = await updateRazorpaySubscription(
          subscription.razorpay_subscription_id,
          newTierId
        );

        if (!updated) {
          console.error('Failed to update Razorpay subscription');
          return null;
        }
      }

      // Update in our database
      const { data, error } = await supabase
        .from('subscriptions')
        .update({
          tier_id: newTierId,
          updated_at: new Date()
        })
        .eq('id', subscriptionId)
        .select()
        .single();

      if (error) {
        console.error('Error updating subscription tier:', error);
        return null;
      }

      // Send update email
      const { data: userData } = await supabase
        .from('users')
        .select('email, first_name, last_name')
        .eq('id', subscription.user_id)
        .single();

      if (userData) {
        const userName = `${userData.first_name} ${userData.last_name}`.trim();
        await sendSubscriptionUpdatedEmail(
          userData.email,
          userName,
          subscription.tier_id,
          newTierId
        );
      }

      return data as Subscription;
    }
  } catch (error) {
    console.error('Error in updateSubscriptionTier:', error);
    return null;
  }
}

// Cancel subscription
export async function cancelSubscription(
  subscriptionId: string,
  cancelImmediately: boolean = false
): Promise<Subscription | null> {
  try {
    // Get the subscription
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('id', subscriptionId)
      .single();

    if (subscriptionError) {
      console.error('Error fetching subscription:', subscriptionError);
      return null;
    }

    // If it's a free subscription, just update the status
    if (subscription.tier_id === TierLevel.FREE) {
      const updateData = cancelImmediately
        ? {
            status: SubscriptionStatus.CANCELED,
            updated_at: new Date()
          }
        : {
            cancel_at_period_end: true,
            updated_at: new Date()
          };

      const { data, error } = await supabase
        .from('subscriptions')
        .update(updateData)
        .eq('id', subscriptionId)
        .select()
        .single();

      if (error) {
        console.error('Error canceling free subscription:', error);
        return null;
      }

      return data as Subscription;
    }

    // For paid subscriptions, cancel in Razorpay
    if (subscription.razorpay_subscription_id) {
      const canceled = await cancelRazorpaySubscription(
        subscription.razorpay_subscription_id,
        cancelImmediately
      );

      if (!canceled) {
        console.error('Failed to cancel Razorpay subscription');
        return null;
      }
    }

    // Update in our database
    const updateData = cancelImmediately
      ? {
          status: SubscriptionStatus.CANCELED,
          updated_at: new Date()
        }
      : {
          cancel_at_period_end: true,
          updated_at: new Date()
        };

    const { data, error } = await supabase
      .from('subscriptions')
      .update(updateData)
      .eq('id', subscriptionId)
      .select()
      .single();

    if (error) {
      console.error('Error canceling subscription in database:', error);
      return null;
    }

    // Send cancellation email
    const { data: userData } = await supabase
      .from('users')
      .select('email, first_name, last_name')
      .eq('id', subscription.user_id)
      .single();

    if (userData) {
      const userName = `${userData.first_name} ${userData.last_name}`.trim();
      await sendSubscriptionCancellationEmail(
        userData.email,
        userName,
        subscription.tier_id,
        new Date(subscription.current_period_end),
        cancelImmediately
      );
    }

    return data as Subscription;
  } catch (error) {
    console.error('Error in cancelSubscription:', error);
    return null;
  }
}

// Get all available tiers
export async function getAllTiers(): Promise<SubscriptionTier[]> {
  const module = await import('../models/SubscriptionTier');
  return module.SUBSCRIPTION_TIERS;
}
